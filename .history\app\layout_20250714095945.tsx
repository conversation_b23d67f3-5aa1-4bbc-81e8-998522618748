import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import Script from "next/script";
import "./globals.css";
import { CustomToaster } from "../src/components/ui/Toast";
import { ThemeProvider } from "../src/contexts/ThemeContext";
import { AuthProvider } from "../src/hooks/use-auth";

const inter = Inter({
  subsets: ["latin"],
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
  style: ["normal", "italic"],
  variable: "--font-inter"
});

export const metadata: Metadata = {
  title: "Better Interest - Smart Savings Platform",
  description:
    "Transform your financial future with Better Interest's intelligent savings platform. Automate your savings, invest wisely, and watch your money grow with better interest rates.",
  keywords:
    "savings app, financial planning, investment, money management, automated savings",
  authors: [{ name: "Koja Save Team" }],
  viewport: "width=device-width, initial-scale=1",
  themeColor: "#22c55e",
  icons: {
    icon: "/logo.svg",
    shortcut: "/logo.svg",
    apple: "/logo.svg",
  },
  openGraph: {
    title: "Koja Save - Smart Savings App",
    description: "Transform your financial future with intelligent savings",
    type: "website",
    locale: "en_US",
    images: [
      {
        url: "/logo.svg",
        width: 1200,
        height: 630,
        alt: "Better Interest Logo",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Koja Save - Smart Savings App",
    description: "Transform your financial future with intelligent savings",
  },
};

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en" className="dark" data-oid="si2j4vl">
      <head data-oid="niakuhl">
        {/* Production */}
        <Script
          type="module"
          src="https://cdn.jsdelivr.net/gh/onlook-dev/onlook@main/apps/web/preload/dist/index.js"
          data-oid="a0w-rg2"
        />

        {/* Development */}
        {/* <Script type="module" src={`http://localhost:8083/?${Math.random()}`} /> */}
      </head>
      <body className={`${inter.variable} font-sans`} data-oid="mwz9mme">
        <ThemeProvider>
          <AuthProvider>
            {children}
            <CustomToaster />
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
