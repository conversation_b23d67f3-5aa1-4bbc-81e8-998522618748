interface RotationalGroup {
  _id: string;
  name: string;
  description: string;
  amountPerInterval: number;
  intervalType: 'daily' | 'weekly' | 'monthly' | 'yearly';
  members: Array<{
    userId: {
      _id: string;
      name: string;
      email: string;
    };
    hasPaid: boolean;
    hasReceivedPayout: boolean;
    joinedAt: string;
  }>;
  currentCycle: number;
  nextPayoutDate: string;
  createdBy: {
    _id: string;
    name: string;
    email: string;
  };
  createdAt: string;
  isActive: boolean;
  payouts: Array<{
    userId: string;
    amount: number;
    date: string;
    cycle: number;
  }>;
}

interface CreateGroupData {
  name: string;
  description: string;
  amountPerInterval: number;
  intervalType: 'daily' | 'weekly' | 'monthly' | 'yearly';
  nextPayoutDate: string;
  createdBy: string;
}


class RotationalSavingsService {
  private baseUrl = process.env.NEXT_PUBLIC_API_URL;
  private apiPath = '/api/rotational-group-savings';

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${this.apiPath}${endpoint}`;
    
    const defaultHeaders = {
      'Content-Type': 'application/json',
    };

    // Add auth token if available
    const token = localStorage.getItem('token');
    if (token) {
      defaultHeaders['Authorization'] = `Bearer ${token}`;
    }

    const config: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }


  // Get all rotational groups
  async getAllGroups(): Promise<RotationalGroup[]> {
    return this.makeRequest<RotationalGroup[]>('/');
  }


  // Get user's rotational groups
  async getMyGroups(): Promise<RotationalGroup[]> {
    return this.makeRequest<RotationalGroup[]>('/my');
  }


  // Create a new rotational group
  async createGroup(groupData: CreateGroupData): Promise<RotationalGroup> {
    return this.makeRequest<RotationalGroup>('/rgs', {
      method: 'POST',
      body: JSON.stringify(groupData),
    });
  }


  // Join a rotational group
  async joinGroup(groupId: string, userId: string): Promise<RotationalGroup> {
    return this.makeRequest<RotationalGroup>(`/${groupId}/join`, {
      method: 'POST',
      body: JSON.stringify({ userId }),
    });
  }


  // Leave a rotational group
  async leaveGroup(groupId: string, userId: string): Promise<{ message: string; group: RotationalGroup }> {
    return this.makeRequest<{ message: string; group: RotationalGroup }>(`/${groupId}/leave`, {
      method: 'POST',
      body: JSON.stringify({ userId }),
    });
  }


  // Make payment for current interval
  async makePayment(groupId: string, userId: string): Promise<{ message: string; group: RotationalGroup }> {
    return this.makeRequest<{ message: string; group: RotationalGroup }>(`/${groupId}/pay`, {
      method: 'POST',
      body: JSON.stringify({ userId }),
    });
  }


  // Get group status/details
  async getGroupStatus(groupId: string): Promise<RotationalGroup> {
    return this.makeRequest<RotationalGroup>(`/${groupId}/status`);
  }


  // Delete a group (creator only)
  async deleteGroup(groupId: string, userId: string): Promise<{ message: string }> {
    return this.makeRequest<{ message: string }>(`/${groupId}`, {
      method: 'DELETE',
      body: JSON.stringify({ userId }),
    });
  }

  // Calculate next payout recipient
  calculateNextPayoutRecipient(group: RotationalGroup): string | null {
    const unpaidMembers = group.members.filter(member => !member.hasReceivedPayout);
    if (unpaidMembers.length === 0) return null;
    
    // Simple round-robin: return the first member who hasn't received payout
    return unpaidMembers[0].userId._id;
  }

  // Calculate total contribution per member
  calculateTotalContribution(group: RotationalGroup): number {
    return group.amountPerInterval * group.members.length;
  }

  // Get payment status for a user in a group
  getUserPaymentStatus(group: RotationalGroup, userId: string): {
    hasPaid: boolean;
    hasReceivedPayout: boolean;
    isNextRecipient: boolean;
  } {
    const member = group.members.find(m => m.userId._id === userId);
    if (!member) {
      return { hasPaid: false, hasReceivedPayout: false, isNextRecipient: false };
    }

    const nextRecipient = this.calculateNextPayoutRecipient(group);
    
    return {
      hasPaid: member.hasPaid,
      hasReceivedPayout: member.hasReceivedPayout,
      isNextRecipient: nextRecipient === userId,
    };
  }

  // Format interval text
  formatIntervalText(intervalType: string): string {
    const intervals = {
      daily: 'Daily',
      weekly: 'Weekly',
      monthly: 'Monthly',
      yearly: 'Yearly'
    };
    return intervals[intervalType as keyof typeof intervals] || intervalType;
  }

  // Calculate days until next payout
  getDaysUntilNextPayout(nextPayoutDate: string): number {
    const now = new Date();
    const payoutDate = new Date(nextPayoutDate);
    const diffTime = payoutDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  }

  // Validate group creation data
  validateGroupData(data: Partial<CreateGroupData>): string[] {
    const errors: string[] = [];

    if (!data.name || data.name.trim().length < 3) {
      errors.push('Group name must be at least 3 characters long');
    }

    if (!data.amountPerInterval || data.amountPerInterval < 1000) {
      errors.push('Amount per interval must be at least ₦1,000');
    }

    if (!data.intervalType || !['daily', 'weekly', 'monthly', 'yearly'].includes(data.intervalType)) {
      errors.push('Invalid interval type');
    }

    if (!data.nextPayoutDate) {
      errors.push('Next payout date is required');
    } else {
      const payoutDate = new Date(data.nextPayoutDate);
      const now = new Date();
      if (payoutDate <= now) {
        errors.push('Next payout date must be in the future');
      }
    }

    return errors;
  }

  // Get group statistics
  getGroupStatistics(groups: RotationalGroup[], userId: string) {
    const userGroups = groups.filter(group => 
      group.members.some(member => member.userId._id === userId)
    );

    const totalContributions = userGroups.reduce((sum, group) => 
      sum + group.amountPerInterval, 0
    );

    const pendingPayments = userGroups.filter(group => {
      const userMember = group.members.find(member => member.userId._id === userId);
      return userMember && !userMember.hasPaid;
    }).length;

    const completedPayouts = userGroups.filter(group => {
      const userMember = group.members.find(member => member.userId._id === userId);
      return userMember && userMember.hasReceivedPayout;
    }).length;

    return {
      totalGroups: userGroups.length,
      totalContributions,
      pendingPayments,
      completedPayouts,
      activeGroups: userGroups.filter(group => group.isActive).length,
    };
  }
}

export const rotationalSavingsService = new RotationalSavingsService();
export type { RotationalGroup, CreateGroupData };
