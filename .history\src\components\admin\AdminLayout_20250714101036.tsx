"use client";

import { motion } from "framer-motion";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import React, { useState } from "react";
import {
  FiBarChart,
  FiBell,
  FiCreditCard,
  FiFileText,
  FiHome,
  FiLogOut,
  FiMenu,
  FiSearch,
  FiSettings,
  FiShield,
  FiUser,
  FiUsers,
  FiX,
} from "react-icons/fi";
import { authService } from "../../services";
import { Badge } from "../ui/Badge";
import { BetterInterestLogo } from "../ui/BetterInterestLogo";
import { Button } from "../ui/Button";
import { Input } from "../ui/Input";
import { showToast } from "../ui/Toast";

interface AdminLayoutProps {
  children: React.ReactNode;
  title?: string;
}

const navigation = [
  { name: "Dashboard", href: "/admin/dashboard", icon: FiHome },
  { name: "User Management", href: "/admin/users", icon: FiUsers },
  {
    name: "Transaction Management",
    href: "/admin/transactions",
    icon: FiCreditCard,
  },
  { name: "KYC Management", href: "/admin/kyc", icon: FiShield },
  {
    name: "Interest & Penalties Settings",
    href: "/admin/interest-penalties",
    icon: FiBarChart,
  },
  { name: "Analytics", href: "/admin/analytics", icon: FiBarChart },
  { name: "Notifications", href: "/admin/notifications", icon: FiBell },
  { name: "Reports", href: "/admin/reports", icon: FiFileText },
  { name: "Settings", href: "/admin/settings", icon: FiSettings },
];

function AdminLayout({ children, title }: AdminLayoutProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const handleLogout = async () => {
    try {
      await authService.logout();
      router.push("/auth/login");
      showToast.success("Logged out successfully");
    } catch (error) {
      showToast.error("Failed to logout");
    }
  };

  const isActive = (href: string) => {
    return pathname === href || pathname.startsWith(href + "/");
  };

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-50 w-64 bg-gray-800/95 backdrop-blur-lg border-r border-gray-700 transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        }`}
      >
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-between h-16 px-6 bg-gray-900">
            <Link
              href="/admin/dashboard"
              className="flex items-center space-x-2"
            >
              <BetterInterestLogo size="sm" variant="light" />
              <span className="text-white font-semibold">Admin Panel</span>
            </Link>
            <button
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden text-gray-400 hover:text-white"
            >
              <FiX className="w-6 h-6" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                    isActive(item.href)
                      ? "bg-green-600 text-white"
                      : "text-gray-300 hover:bg-gray-700 hover:text-white"
                  }`}
                  onClick={() => setSidebarOpen(false)}
                >
                  <Icon className="mr-3 w-5 h-5" />
                  {item.name}
                </Link>
              );
            })}
          </nav>

          {/* User info */}
          <div className="p-4 border-t border-gray-700">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center">
                <FiUser className="text-white" />
              </div>
              <div>
                <p className="text-white font-medium">Admin User</p>
                <p className="text-gray-400 text-sm"><EMAIL></p>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleLogout}
              className="w-full"
            >
              <FiLogOut className="mr-2" />
              Logout
            </Button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:ml-64">
        {/* Top bar */}
        <header className="bg-gray-800 border-b border-gray-700">
          <div className="flex items-center justify-between h-16 px-6">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden text-gray-400 hover:text-white"
              >
                <FiMenu className="w-6 h-6" />
              </button>
              {title && (
                <h1 className="text-xl font-semibold text-white">{title}</h1>
              )}
            </div>

            <div className="flex items-center space-x-4">
              {/* Search */}
              <div className="hidden md:block">
                <Input
                  type="text"
                  placeholder="Search..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  icon={<FiSearch />}
                  className="w-64"
                />
              </div>

              {/* Notifications */}
              <button className="relative text-gray-400 hover:text-white">
                <FiBell className="w-6 h-6" />
                <Badge
                  variant="error"
                  className="absolute -top-2 -right-2 w-5 h-5 text-xs flex items-center justify-center"
                >
                  3
                </Badge>
              </button>

              {/* Profile */}
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center">
                  <FiUser className="text-white text-sm" />
                </div>
                <span className="hidden md:block text-white text-sm">
                  Admin
                </span>
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="p-4 sm:p-6 lg:p-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="max-w-7xl mx-auto"
          >
            {children}
          </motion.div>
        </main>
      </div>

      {/* Intercom Widget */}
      {/* IntercomWidget removed - not implemented */}

      {/* Help Button */}
      {/* <HelpButton /> */}
    </div>
  );
}

export default AdminLayout;
