"use client";

import React, { useEffect, useState } from "react";
import AdminLayout from "../../../src/components/admin/AdminLayout";
import Card from "../../../src/components/ui/Card";
import Button from "../../../src/components/ui/Button";



type PlanType = 'individual' | 'target' | 'group' | 'rotational';

interface PlanInterest {
  planType: PlanType;
  rate: number | null;
}

interface FeeStructure {
  id: string;
  name: string;
  description: string;
  feeType: string;
  amount: number;
  percentage: number;
  minimumAmount: number;
  maximumAmount: number;
  isActive: boolean;
  category: string;
  appliesTo: string;
  conditions: string;
  gracePeriod: string;
  autoApply: boolean;
  refundable: boolean;
  taxInclusive: boolean;
  totalCollected: number;
  transactionsCount: number;
  createdAt: string;
}


export default function InterestPenaltiesPage() {
  const [globalInterest, setGlobalInterest] = useState<number | null>(null);
  const [planInterests, setPlanInterests] = useState<Record<PlanType, number | null>>({
    individual: null,
    target: null,
    group: null,
    rotational: null,
  });
  const [fees, setFees] = useState<FeeStructure[]>([]);
  const [withdrawalPenalty, setWithdrawalPenalty] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [editingPlan, setEditingPlan] = useState<PlanType | null>(null);
  const [newPlanRate, setNewPlanRate] = useState("");
  const [editingGlobal, setEditingGlobal] = useState(false);
  const [newGlobalRate, setNewGlobalRate] = useState("");
  const [newWithdrawalPenalty, setNewWithdrawalPenalty] = useState("");

  const apiUrl = process.env.NEXT_PUBLIC_API_URL?.replace(/\/$/, "");

  // Helper to get token from localStorage (now uses 'auth_token')
  const getToken = () =>
    (typeof window !== 'undefined' && localStorage.getItem('auth_token')) || '';

  useEffect(() => {
    fetchAll();
  }, []);

  const fetchAll = async () => {
    setLoading(true);
    setError(null);
    try {
      const token = getToken();
      // Fetch global interest rate
      const globalRes = await fetch(`${apiUrl}/api/withdrawalset/global-interest-rate`);
      const globalData = await globalRes.json();
      setGlobalInterest(typeof globalData.rate === 'number' ? globalData.rate : null);
      setNewGlobalRate(globalData.rate?.toString() || "");

      // Fetch plan-specific interest rates
      const planTypes: PlanType[] = ['individual', 'target', 'group', 'rotational'];
      const planInterestObj: Record<PlanType, number | null> = { individual: null, target: null, group: null, rotational: null };
      for (const planType of planTypes) {
        const res = await fetch(`${apiUrl}/api/withdrawalset/plan-interest-rate/${planType}`);
        const data = await res.json();
        planInterestObj[planType] = typeof data.rate === 'number' ? data.rate : null;
      }
      setPlanInterests(planInterestObj);

      // Fetch withdrawal penalty (public GET)
      const penaltyRes = await fetch(`${apiUrl}/api/withdrawalset/withdrawal-penalty`);
      const penaltyData = await penaltyRes.json();
      setWithdrawalPenalty(penaltyData.penalty);
      setNewWithdrawalPenalty(penaltyData.penalty?.toString() || "");

      // Fetch all fees (assume public)
      const feesRes = await fetch(`${apiUrl}/api/admin/fees-management/fees`, {
        headers: token ? { Authorization: `Bearer ${token}` } : {},
      });
      const feesData = await feesRes.json();
      setFees(Array.isArray(feesData) ? feesData : []);
    } catch (err) {
      setError("Failed to fetch settings");
    } finally {
      setLoading(false);
    }
  };

  const handleUpdatePlanRate = async (planType: PlanType) => {
    try {
      const token = getToken();
      const res = await fetch(`${apiUrl}/api/withdrawalset/plan-interest-rate/${planType}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
        },
        body: JSON.stringify({ rate: Number(newPlanRate) }),
      });
      if (!res.ok) throw new Error();
      setEditingPlan(null);
      setNewPlanRate("");
      fetchAll();
    } catch {
      setError("Failed to update plan interest rate");
    }
  };

  const handleUpdateGlobalRate = async () => {
    try {
      const token = getToken();
      const res = await fetch(`${apiUrl}/api/withdrawalset/global-interest-rate`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
        },
        body: JSON.stringify({ rate: Number(newGlobalRate) }),
      });
      if (!res.ok) throw new Error();
      setEditingGlobal(false);
      setNewGlobalRate("");
      fetchAll();
    } catch {
      setError("Failed to update global interest rate");
    }
  };

  const handleUpdateWithdrawalPenalty = async () => {
    try {
      const token = getToken();
      const res = await fetch(`${apiUrl}/api/withdrawalset/withdrawal-penalty`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
        },
        body: JSON.stringify({ penalty: Number(newWithdrawalPenalty) }),
      });
      if (!res.ok) throw new Error();
      fetchAll();
    } catch {
      setError("Failed to update withdrawal penalty");
    }
  };

  // Penalties & Fees Table
  const penaltyFees = fees.filter(fee => fee.category === "penalty");

  return (
    <AdminLayout title="Interest & Penalties Settings">
      <div className="max-w-3xl mx-auto space-y-8">
        <Card className="bg-gray-800 border-gray-700 p-6">
          <h2 className="text-xl font-bold text-white mb-4">Global Interest Rate</h2>
          <div className="flex items-center space-x-4 mb-4">
            {editingGlobal ? (
              <>
                <input
                  type="number"
                  min={0}
                  max={100}
                  value={newGlobalRate}
                  onChange={e => setNewGlobalRate(e.target.value)}
                  className="bg-gray-900 text-white border border-gray-600 rounded px-3 py-2 w-32"
                />
                <span className="text-gray-400">%</span>
                <Button size="sm" onClick={handleUpdateGlobalRate}>Save</Button>
                <Button size="sm" variant="outline" onClick={() => setEditingGlobal(false)}>Cancel</Button>
              </>
            ) : (
              <>
                <span className="text-gray-400">{globalInterest !== null ? `${globalInterest}%` : "Not set"}</span>
                <Button size="sm" onClick={() => { setEditingGlobal(true); setNewGlobalRate(globalInterest?.toString() || ""); }}>Edit</Button>
              </>
            )}
          </div>
          <h2 className="text-lg font-semibold text-white mb-2">Plan-Specific Interest Rates</h2>
          <div className="space-y-4">
            {(['individual', 'target', 'group', 'rotational'] as PlanType[]).map(planType => (
              <div key={planType} className="flex items-center space-x-4">
                <span className="text-white w-32 capitalize">{planType}</span>
                {editingPlan === planType ? (
                  <>
                    <input
                      type="number"
                      min={0}
                      max={100}
                      value={newPlanRate}
                      onChange={e => setNewPlanRate(e.target.value)}
                      className="bg-gray-900 text-white border border-gray-600 rounded px-3 py-2 w-32"
                    />
                    <span className="text-gray-400">%</span>
                    <Button size="sm" onClick={() => handleUpdatePlanRate(planType)}>Save</Button>
                    <Button size="sm" variant="outline" onClick={() => setEditingPlan(null)}>Cancel</Button>
                  </>
                ) : (
                  <>
                    <span className="text-gray-400">{planInterests[planType] !== null ? `${planInterests[planType]}%` : "Not set"}</span>
                    <Button size="sm" onClick={() => { setEditingPlan(planType); setNewPlanRate(planInterests[planType]?.toString() || ""); }}>Edit</Button>
                  </>
                )}
              </div>
            ))}
          </div>
        </Card>

        <Card className="bg-gray-800 border-gray-700 p-6">
          <h2 className="text-xl font-bold text-white mb-4">Withdrawal Penalty</h2>
          <div className="flex items-center space-x-4">
            <input
              type="number"
              min={0}
              max={100}
              value={newWithdrawalPenalty}
              onChange={e => setNewWithdrawalPenalty(e.target.value)}
              className="bg-gray-900 text-white border border-gray-600 rounded px-3 py-2 w-32"
            />
            <span className="text-gray-400">%</span>
            <Button onClick={handleUpdateWithdrawalPenalty} size="sm">Update</Button>
          </div>
          <p className="text-gray-400 mt-2 text-sm">Current: <span className="text-white font-semibold">{withdrawalPenalty}%</span></p>
        </Card>

        <Card className="bg-gray-800 border-gray-700 p-6">
          <h2 className="text-xl font-bold text-white mb-4">Penalty Fees</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm text-left text-gray-400">
              <thead>
                <tr>
                  <th className="px-4 py-2">Name</th>
                  <th className="px-4 py-2">Description</th>
                  <th className="px-4 py-2">Type</th>
                  <th className="px-4 py-2">Amount</th>
                  <th className="px-4 py-2">%</th>
                  <th className="px-4 py-2">Min</th>
                  <th className="px-4 py-2">Max</th>
                  <th className="px-4 py-2">Active</th>
                </tr>
              </thead>
              <tbody>
                {penaltyFees.map(fee => (
                  <tr key={fee.id} className="border-b border-gray-700">
                    <td className="px-4 py-2 text-white">{fee.name}</td>
                    <td className="px-4 py-2">{fee.description}</td>
                    <td className="px-4 py-2">{fee.feeType}</td>
                    <td className="px-4 py-2">₦{fee.amount}</td>
                    <td className="px-4 py-2">{fee.percentage}</td>
                    <td className="px-4 py-2">₦{fee.minimumAmount}</td>
                    <td className="px-4 py-2">₦{fee.maximumAmount}</td>
                    <td className="px-4 py-2">{fee.isActive ? "Yes" : "No"}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>

        {error && <div className="text-red-400 text-center">{error}</div>}
        {loading && <div className="text-gray-400 text-center">Loading...</div>}
      </div>
    </AdminLayout>
  );
}
