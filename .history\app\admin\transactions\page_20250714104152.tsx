"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FiSearch,
  FiFilter,
  FiDownload,
  FiEye,
  FiCheck,
  FiX,
  FiClock,
  FiDollarSign,
  FiTrendingUp,
  FiTrendingDown,
  FiRefreshCw,
} from 'react-icons/fi';
import AdminLayout from '../../../src/components/admin/AdminLayout';
import { Card3D } from '../../../src/components/ui/Card3D';
import { PrimaryButton, OutlineButton } from '../../../src/components/ui/AnimatedButton';
import { showToast } from '../../../src/components/ui/Toast';

interface Transaction {
  id: string;
  userId: string;
  userName: string;
  type: 'deposit' | 'withdrawal' | 'transfer' | 'interest';
  amount: number;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  description: string;
  createdAt: string;
  updatedAt: string;
  reference: string;
  paymentMethod?: string;
}


const apiUrl = process.env.NEXT_PUBLIC_API_URL?.replace(/\/$/, "");

export default function TransactionManagementPage() {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [filteredTransactions, setFilteredTransactions] = useState<Transaction[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(false);


  // Fetch transactions from backend
  useEffect(() => {
    const fetchTransactions = async () => {
      setIsLoading(true);
      try {
        const res = await fetch(`${apiUrl}/api/transactions/all?limit=100`);
        const data = await res.json();
        // Map backend data to Transaction[]
        const txs: Transaction[] = (data.transactions || []).map((t: any) => ({
          id: t._id,
          userId: t.userId?._id || t.userId || '',
          userName: t.userId ? `${t.userId.firstName || ''} ${t.userId.lastName || ''}`.trim() : '',
          type: t.type,
          amount: t.amount,
          status: t.status || 'completed',
          description: t.description,
          createdAt: t.createdAt,
          updatedAt: t.updatedAt || t.createdAt,
          reference: t.reference,
          paymentMethod: t.paymentMethod || '',
        }));
        setTransactions(txs);
        setFilteredTransactions(txs);
      } catch (error) {
        showToast.error('Failed to fetch transactions');
      } finally {
        setIsLoading(false);
      }
    };
    fetchTransactions();
  }, []);

  // Filter transactions based on search and filters
  useEffect(() => {
    let filtered = transactions;

    if (searchQuery) {
      filtered = filtered.filter(
        (transaction) =>
          transaction.userName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          transaction.reference.toLowerCase().includes(searchQuery.toLowerCase()) ||
          transaction.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter((transaction) => transaction.status === statusFilter);
    }

    if (typeFilter !== 'all') {
      filtered = filtered.filter((transaction) => transaction.type === typeFilter);
    }

    setFilteredTransactions(filtered);
  }, [transactions, searchQuery, statusFilter, typeFilter]);

  const handleApproveTransaction = async (transactionId: string) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setTransactions(prev =>
        prev.map(transaction =>
          transaction.id === transactionId
            ? { ...transaction, status: 'completed' as const, updatedAt: new Date().toISOString() }
            : transaction
        )
      );
      
      showToast.success('Transaction approved successfully');
    } catch (error) {
      showToast.error('Failed to approve transaction');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRejectTransaction = async (transactionId: string) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setTransactions(prev =>
        prev.map(transaction =>
          transaction.id === transactionId
            ? { ...transaction, status: 'failed' as const, updatedAt: new Date().toISOString() }
            : transaction
        )
      );
      
      showToast.success('Transaction rejected');
    } catch (error) {
      showToast.error('Failed to reject transaction');
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <FiCheck className="text-green-400" />;
      case 'pending':
        return <FiClock className="text-yellow-400" />;
      case 'failed':
        return <FiX className="text-red-400" />;
      case 'cancelled':
        return <FiX className="text-gray-400" />;
      default:
        return <FiClock className="text-gray-400" />;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'deposit':
        return <FiTrendingUp className="text-green-400" />;
      case 'withdrawal':
        return <FiTrendingDown className="text-red-400" />;
      case 'interest':
        return <FiDollarSign className="text-blue-400" />;
      default:
        return <FiRefreshCw className="text-gray-400" />;
    }
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-NG', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Calculate summary stats
  const totalTransactions = transactions.length;
  const pendingTransactions = transactions.filter(t => t.status === 'pending').length;
  const totalVolume = transactions
    .filter(t => t.status === 'completed')
    .reduce((sum, t) => sum + t.amount, 0);
  const successRate = transactions.length > 0 
    ? (transactions.filter(t => t.status === 'completed').length / transactions.length) * 100 
    : 0;

  return (
    <AdminLayout title="Transaction Management">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">Transaction Management</h1>
            <p className="text-gray-400 mt-2">Monitor and manage all platform transactions</p>
          </div>
          <div className="flex space-x-2">
            <OutlineButton>
              <FiDownload className="mr-2" />
              Export
            </OutlineButton>
            <PrimaryButton>
              <FiRefreshCw className="mr-2" />
              Refresh
            </PrimaryButton>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card3D className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Transactions</p>
                <p className="text-2xl font-bold text-white">{totalTransactions}</p>
              </div>
              <FiRefreshCw className="text-blue-500 text-3xl" />
            </div>
          </Card3D>

          <Card3D className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Pending Approval</p>
                <p className="text-2xl font-bold text-white">{pendingTransactions}</p>
              </div>
              <FiClock className="text-yellow-500 text-3xl" />
            </div>
          </Card3D>

          <Card3D className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Volume</p>
                <p className="text-2xl font-bold text-white">{formatAmount(totalVolume)}</p>
              </div>
              <FiDollarSign className="text-green-500 text-3xl" />
            </div>
          </Card3D>

          <Card3D className="bg-gray-800 border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Success Rate</p>
                <p className="text-2xl font-bold text-white">{successRate.toFixed(1)}%</p>
              </div>
              <FiTrendingUp className="text-green-500 text-3xl" />
            </div>
          </Card3D>
        </div>

        {/* Filters and Search */}
        <Card3D className="bg-gray-800 border-gray-700 p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search transactions..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white"
                />
              </div>
            </div>
            <div className="flex gap-4">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="completed">Completed</option>
                <option value="failed">Failed</option>
                <option value="cancelled">Cancelled</option>
              </select>
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white"
              >
                <option value="all">All Types</option>
                <option value="deposit">Deposits</option>
                <option value="withdrawal">Withdrawals</option>
                <option value="transfer">Transfers</option>
                <option value="interest">Interest</option>
              </select>
            </div>
          </div>
        </Card3D>

        {/* Transactions Table */}
        <Card3D className="bg-gray-800 border-gray-700 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Transaction
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-700">
                {filteredTransactions.map((transaction) => (
                  <motion.tr
                    key={transaction.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="hover:bg-gray-700/50"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getTypeIcon(transaction.type)}
                        <div className="ml-3">
                          <div className="text-sm font-medium text-white">
                            {transaction.reference}
                          </div>
                          <div className="text-sm text-gray-400">
                            {transaction.description}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-white">{transaction.userName}</div>
                      <div className="text-sm text-gray-400">{transaction.userId}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-white">
                        {formatAmount(transaction.amount)}
                      </div>
                      {transaction.paymentMethod && (
                        <div className="text-sm text-gray-400">
                          {transaction.paymentMethod}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getStatusIcon(transaction.status)}
                        <span className={`ml-2 text-sm capitalize ${
                          transaction.status === 'completed' ? 'text-green-400' :
                          transaction.status === 'pending' ? 'text-yellow-400' :
                          transaction.status === 'failed' ? 'text-red-400' :
                          'text-gray-400'
                        }`}>
                          {transaction.status}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                      {formatDate(transaction.createdAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button className="text-blue-400 hover:text-blue-300">
                          <FiEye />
                        </button>
                        {transaction.status === 'pending' && (
                          <>
                            <button
                              onClick={() => handleApproveTransaction(transaction.id)}
                              disabled={isLoading}
                              className="text-green-400 hover:text-green-300 disabled:opacity-50"
                            >
                              <FiCheck />
                            </button>
                            <button
                              onClick={() => handleRejectTransaction(transaction.id)}
                              disabled={isLoading}
                              className="text-red-400 hover:text-red-300 disabled:opacity-50"
                            >
                              <FiX />
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card3D>
      </div>
    </AdminLayout>
  );
}
