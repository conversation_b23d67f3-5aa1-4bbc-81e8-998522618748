"use client";

import React, { useEffect, useState } from "react";
import AdminLayout from "../../../src/components/admin/AdminLayout";
import Card from "../../../src/components/ui/Card";
import Button from "../../../src/components/ui/Button";

interface PlanRate {
  planType: string;
  rate: number | null;
}

export default function InterestPenaltiesPage() {
  const [globalInterest, setGlobalInterest] = useState<number | null>(null);
  const [withdrawalPenalty, setWithdrawalPenalty] = useState<number | null>(null);
  const [planRates, setPlanRates] = useState<PlanRate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [newGlobalInterest, setNewGlobalInterest] = useState("");
  const [newWithdrawalPenalty, setNewWithdrawalPenalty] = useState("");
  const [editingPlan, setEditingPlan] = useState<string | null>(null);
  const [newPlanRate, setNewPlanRate] = useState("");

  useEffect(() => {
    fetchAll();
  }, []);

  const fetchAll = async () => {
    setLoading(true);
    setError(null);
    try {
      // Fetch global interest
      const globalRes = await fetch("/api/settings/global-interest-rate");
      const globalData = await globalRes.json();
      setGlobalInterest(globalData.rate);
      setNewGlobalInterest(globalData.rate?.toString() || "");

      // Fetch withdrawal penalty
      const penaltyRes = await fetch("/api/settings/withdrawal-penalty");
      const penaltyData = await penaltyRes.json();
      setWithdrawalPenalty(penaltyData.penalty);
      setNewWithdrawalPenalty(penaltyData.penalty?.toString() || "");

      // Fetch plan types (example: you may want to fetch from backend, here hardcoded)
      const planTypes = ["basic", "premium", "flexible"];
      const planRatesFetched: PlanRate[] = [];
      for (const planType of planTypes) {
        const res = await fetch(`/api/settings/plan-interest-rate/${planType}`);
        const data = await res.json();
        planRatesFetched.push({ planType, rate: data.rate });
      }
      setPlanRates(planRatesFetched);
    } catch (err) {
      setError("Failed to fetch settings");
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateGlobalInterest = async () => {
    try {
      const res = await fetch("/api/settings/global-interest-rate", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ rate: Number(newGlobalInterest) }),
      });
      if (!res.ok) throw new Error();
      fetchAll();
    } catch {
      setError("Failed to update global interest rate");
    }
  };

  const handleUpdateWithdrawalPenalty = async () => {
    try {
      const res = await fetch("/api/settings/withdrawal-penalty", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ penalty: Number(newWithdrawalPenalty) }),
      });
      if (!res.ok) throw new Error();
      fetchAll();
    } catch {
      setError("Failed to update withdrawal penalty");
    }
  };

  const handleUpdatePlanRate = async (planType: string) => {
    try {
      const res = await fetch(`/api/settings/plan-interest-rate/${planType}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ rate: Number(newPlanRate) }),
      });
      if (!res.ok) throw new Error();
      setEditingPlan(null);
      setNewPlanRate("");
      fetchAll();
    } catch {
      setError("Failed to update plan interest rate");
    }
  };

  return (
    <AdminLayout title="Interest & Penalties Settings">
      <div className="max-w-2xl mx-auto space-y-8">
        <Card className="bg-gray-800 border-gray-700 p-6">
          <h2 className="text-xl font-bold text-white mb-4">Global Interest Rate</h2>
          <div className="flex items-center space-x-4">
            <input
              type="number"
              min={0}
              max={100}
              value={newGlobalInterest}
              onChange={e => setNewGlobalInterest(e.target.value)}
              className="bg-gray-900 text-white border border-gray-600 rounded px-3 py-2 w-32"
            />
            <span className="text-gray-400">%</span>
            <Button onClick={handleUpdateGlobalInterest} size="sm">Update</Button>
          </div>
          <p className="text-gray-400 mt-2 text-sm">Current: <span className="text-white font-semibold">{globalInterest}%</span></p>
        </Card>

        <Card className="bg-gray-800 border-gray-700 p-6">
          <h2 className="text-xl font-bold text-white mb-4">Withdrawal Penalty</h2>
          <div className="flex items-center space-x-4">
            <input
              type="number"
              min={0}
              max={100}
              value={newWithdrawalPenalty}
              onChange={e => setNewWithdrawalPenalty(e.target.value)}
              className="bg-gray-900 text-white border border-gray-600 rounded px-3 py-2 w-32"
            />
            <span className="text-gray-400">%</span>
            <Button onClick={handleUpdateWithdrawalPenalty} size="sm">Update</Button>
          </div>
          <p className="text-gray-400 mt-2 text-sm">Current: <span className="text-white font-semibold">{withdrawalPenalty}%</span></p>
        </Card>

        <Card className="bg-gray-800 border-gray-700 p-6">
          <h2 className="text-xl font-bold text-white mb-4">Plan-Specific Interest Rates</h2>
          <div className="space-y-4">
            {planRates.map(pr => (
              <div key={pr.planType} className="flex items-center space-x-4">
                <span className="text-white w-32 capitalize">{pr.planType}</span>
                {editingPlan === pr.planType ? (
                  <>
                    <input
                      type="number"
                      min={0}
                      max={100}
                      value={newPlanRate}
                      onChange={e => setNewPlanRate(e.target.value)}
                      className="bg-gray-900 text-white border border-gray-600 rounded px-3 py-2 w-32"
                    />
                    <span className="text-gray-400">%</span>
                    <Button size="sm" onClick={() => handleUpdatePlanRate(pr.planType)}>Save</Button>
                    <Button size="sm" variant="outline" onClick={() => setEditingPlan(null)}>Cancel</Button>
                  </>
                ) : (
                  <>
                    <span className="text-gray-400">{pr.rate !== null ? `${pr.rate}%` : "(uses global)"}</span>
                    <Button size="sm" onClick={() => { setEditingPlan(pr.planType); setNewPlanRate(pr.rate?.toString() || ""); }}>Edit</Button>
                  </>
                )}
              </div>
            ))}
          </div>
        </Card>

        {error && <div className="text-red-400 text-center">{error}</div>}
        {loading && <div className="text-gray-400 text-center">Loading...</div>}
      </div>
    </AdminLayout>
  );
}
