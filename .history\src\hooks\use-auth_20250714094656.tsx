
"use client";

import { useEffect, useState } from 'react';

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: 'user' | 'admin';
  planType?: string;
  totalSavings?: number;
  kycStatus?: 'pending' | 'approved' | 'rejected';
  createdAt?: string;
  intercomHash?: string;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  logout: () => void;
}

export function useAuth(): AuthState {
  const logout = () => {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');
    setAuthState(prev => ({
      ...prev,
      user: null,
      isAuthenticated: false,
      isLoading: false,
    }));
    // Redirect to login page
    window.location.href = '/auth/login';
  };

  const [authState, setAuthState] = useState<Omit<AuthState, 'logout'>>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
  });

  useEffect(() => {
    // Check for existing auth token and validate it
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem('auth_token');
        const userData = localStorage.getItem('user_data');

        if (token && userData) {
          // Validate token with backend
          try {
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080'}/api/auth/verify`, {
              headers: {
                'Authorization': `Bearer ${token}`,
              },
            });

            if (response.ok) {
              const user = JSON.parse(userData);
              setAuthState({
                user,
                isAuthenticated: true,
                isLoading: false,
              });
            } else {
              // Token is invalid, clear storage
              localStorage.removeItem('auth_token');
              localStorage.removeItem('user_data');
              setAuthState({
                user: null,
                isAuthenticated: false,
                isLoading: false,
              });
            }
          } catch (error) {
            // If verification fails, still use local data but log the error
            console.warn('Token verification failed, using local data:', error);
            const user = JSON.parse(userData);
            setAuthState({
              user,
              isAuthenticated: true,
              isLoading: false,
            });
          }
        } else {
          setAuthState({
            user: null,
            isAuthenticated: false,
            isLoading: false,
          });
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        setAuthState({
          user: null,
          isAuthenticated: false,
          isLoading: false,
        });
      }
    };

    // Add a small delay to prevent hydration issues
    const timer = setTimeout(checkAuth, 100);

    return () => clearTimeout(timer);
  }, []);

  return {
    ...authState,
    logout,
  };
}

// Mock login function
export const login = async (email: string, password: string): Promise<User> => {
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  const mockUser: User = {
    id: '1',
    firstName: 'John',
    lastName: 'Doe',
    email,
    role: 'user',
    planType: 'premium',
    totalSavings: 150000,
    kycStatus: 'approved',
    createdAt: new Date().toISOString(),
  };

  // Store in localStorage
  localStorage.setItem('auth_token', 'mock_token_123');
  localStorage.setItem('user_data', JSON.stringify(mockUser));
  
  return mockUser;
};

// Mock logout function
export const logout = (): void => {
  localStorage.removeItem('auth_token');
  localStorage.removeItem('user_data');
  window.location.href = '/';
};

